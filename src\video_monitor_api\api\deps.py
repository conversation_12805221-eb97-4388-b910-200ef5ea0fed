"""
API依赖注入

提供通用的依赖注入函数，如认证、数据库连接等。
"""

import logging
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt

from ..config.settings import get_settings
from ..config.database import get_supabase_client
from ..models.api_models import UserInfo
from ..core.database_service import DatabaseService

logger = logging.getLogger(__name__)

# HTTP Bearer认证方案
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> UserInfo:
    """
    获取当前认证用户信息
    
    Args:
        credentials: HTTP Bearer认证凭据
        
    Returns:
        用户信息
        
    Raises:
        HTTPException: 认证失败时抛出401错误
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        settings = get_settings()
        
        # 解码JWT token
        payload = jwt.decode(
            credentials.credentials,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
            
        # 这里可以从数据库获取更详细的用户信息
        user_info = UserInfo(
            user_id=payload.get("user_id", username),
            username=username,
            token=credentials.credentials,
            expires_at=payload.get("exp"),
            is_admin=payload.get("is_admin", False)
        )
        
        return user_info
        
    except JWTError:
        raise credentials_exception


async def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[UserInfo]:
    """
    获取当前用户信息（可选）
    
    Args:
        credentials: HTTP Bearer认证凭据（可选）
        
    Returns:
        用户信息或None
    """
    if credentials is None:
        return None
    
    try:
        return await get_current_user(credentials)
    except HTTPException:
        return None


def get_database():
    """
    获取数据库客户端依赖

    Returns:
        Supabase客户端实例
    """
    return get_supabase_client()


def get_database_service() -> DatabaseService:
    """
    获取数据库服务依赖

    Returns:
        数据库服务实例
    """
    supabase_client = get_supabase_client()
    return DatabaseService(supabase_client)


def get_app_settings():
    """
    获取应用配置依赖

    Returns:
        应用配置实例
    """
    return get_settings()
