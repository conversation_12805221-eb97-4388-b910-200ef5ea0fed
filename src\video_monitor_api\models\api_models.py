"""
API请求和响应模型

定义所有API端点的请求和响应数据结构。
"""

from datetime import datetime
from typing import List, Optional, Any, Dict, Union
from pydantic import BaseModel, Field


class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = True
    message: str = "操作成功"
    timestamp: datetime = Field(default_factory=datetime.now)


class ApiResponse(BaseResponse):
    """通用API响应模型"""
    data: Optional[Any] = None
    error: Optional[str] = None
    
    @classmethod
    def success_response(cls, data: Any = None, message: str = "操作成功"):
        """创建成功响应"""
        return cls(success=True, message=message, data=data)
    
    @classmethod
    def error_response(cls, error: str, message: str = "操作失败"):
        """创建错误响应"""
        return cls(success=False, message=message, error=error)


# 认证相关模型
class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


class LoginResponse(BaseModel):
    """登录响应模型"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")
    user_info: Dict[str, Any] = Field(..., description="用户信息")


class UserInfo(BaseModel):
    """用户信息模型"""
    user_id: str = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    token: str = Field(..., description="认证令牌")
    expires_at: Optional[int] = Field(None, description="过期时间戳")
    is_admin: bool = Field(default=False, description="是否为管理员")


# 通道相关模型
class ChannelInfo(BaseModel):
    """通道信息模型"""
    channel_id: str = Field(..., description="通道ID")
    channel_name: str = Field(..., description="通道名称")
    org_name: str = Field(..., description="组织名称")
    org_id: str = Field(..., description="组织ID")
    org_level: int = Field(default=0, description="组织层级")
    status: str = Field(default="unknown", description="通道状态")
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class ChannelResponse(BaseModel):
    """通道响应模型"""
    channels: List[ChannelInfo] = Field(..., description="通道列表")
    total: int = Field(..., description="总数量")
    page: int = Field(default=1, description="页码")
    page_size: int = Field(default=100, description="每页大小")


# 监控URL相关模型
class MonitorUrlInfo(BaseModel):
    """监控URL信息模型"""
    channel_id: str = Field(..., description="通道ID")
    channel_name: Optional[str] = Field(None, description="通道名称")
    org_name: Optional[str] = Field(None, description="组织名称")
    scheme: str = Field(..., description="协议类型")
    sub_type: int = Field(..., description="码流类型")
    url: str = Field(..., description="监控URL")
    status: str = Field(default="active", description="URL状态")
    created_at: Optional[datetime] = None


class MonitorUrlRequest(BaseModel):
    """监控URL请求模型"""
    channel_id: str = Field(..., description="通道ID")
    scheme: str = Field(default="RTSP", description="协议类型")
    sub_type: int = Field(default=0, description="码流类型")


class MonitorUrlResponse(BaseModel):
    """监控URL响应模型"""
    urls: List[MonitorUrlInfo] = Field(..., description="监控URL列表")
    total: int = Field(..., description="总数量")


class BatchMonitorUrlRequest(BaseModel):
    """批量监控URL请求模型"""
    channel_ids: List[str] = Field(..., description="通道ID列表")
    schemes: List[str] = Field(default=["RTSP"], description="协议类型列表")
    sub_types: List[int] = Field(default=[0], description="码流类型列表")


# 实时功能相关模型
class WebSocketMessage(BaseModel):
    """WebSocket消息模型"""
    type: str = Field(..., description="消息类型")
    data: Any = Field(..., description="消息数据")
    timestamp: datetime = Field(default_factory=datetime.now)


class AlertInfo(BaseModel):
    """告警信息模型"""
    id: str = Field(..., description="告警ID")
    alert_type: str = Field(..., description="告警类型")
    channel_id: Optional[str] = Field(None, description="相关通道ID")
    message: str = Field(..., description="告警消息")
    severity: str = Field(..., description="严重程度")
    resolved: bool = Field(default=False, description="是否已解决")
    created_at: datetime = Field(default_factory=datetime.now)


# 系统管理相关模型
class SystemStatus(BaseModel):
    """系统状态模型"""
    service_name: str = Field(..., description="服务名称")
    version: str = Field(..., description="版本号")
    status: str = Field(..., description="运行状态")
    uptime: int = Field(..., description="运行时间（秒）")
    database_status: str = Field(..., description="数据库状态")
    monitor_system_status: str = Field(..., description="监控系统状态")
    active_connections: int = Field(default=0, description="活跃连接数")
    total_channels: int = Field(default=0, description="总通道数")


class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="健康状态")
    checks: Dict[str, str] = Field(..., description="各组件检查结果")
    timestamp: datetime = Field(default_factory=datetime.now)


# 视频流相关模型
class StreamInfo(BaseModel):
    """视频流信息模型"""
    channel_id: str = Field(..., description="通道ID")
    stream_url: str = Field(..., description="流地址")
    scheme: str = Field(..., description="协议类型")
    resolution: Optional[str] = Field(None, description="分辨率")
    fps: Optional[int] = Field(None, description="帧率")
    bitrate: Optional[int] = Field(None, description="码率")
    status: str = Field(..., description="流状态")
