"""
API路由模块

包含所有API路由的定义和注册。
"""

from fastapi import APIRouter

from .auth import router as auth_router
from .channels import router as channels_router
from .monitor import router as monitor_router
from .realtime import router as realtime_router
from .system import router as system_router

# 创建主路由器
api_router = APIRouter(prefix="/api/v1")

# 注册所有子路由
api_router.include_router(auth_router)
api_router.include_router(channels_router)
api_router.include_router(monitor_router)
api_router.include_router(realtime_router)
api_router.include_router(system_router)

__all__ = [
    "api_router",
    "auth_router",
    "channels_router", 
    "monitor_router",
    "realtime_router",
    "system_router",
]
