#!/usr/bin/env python3
"""
API测试脚本

用于测试视频监控API服务的基本功能
"""

import asyncio
import aiohttp
import json
import sys
import os

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

class APITester:
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = None
        self.token = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_health_check(self):
        """测试健康检查"""
        print("🔍 测试健康检查...")
        try:
            async with self.session.get(f"{self.base_url}/system/health") as resp:
                data = await resp.json()
                print(f"✅ 健康检查: {data['status']}")
                return resp.status == 200
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
            return False
    
    async def test_system_status(self):
        """测试系统状态（需要认证）"""
        print("🔍 测试系统状态...")
        if not self.token:
            print("⚠️  跳过系统状态测试（需要认证）")
            return True
        
        try:
            headers = {"Authorization": f"Bearer {self.token}"}
            async with self.session.get(f"{self.base_url}/system/status", headers=headers) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ 系统状态: {data['data']['status']}")
                    return True
                else:
                    print(f"❌ 系统状态测试失败: {resp.status}")
                    return False
        except Exception as e:
            print(f"❌ 系统状态测试异常: {e}")
            return False
    
    async def test_login(self, username: str = "test", password: str = "test"):
        """测试登录功能"""
        print("🔍 测试登录功能...")
        try:
            login_data = {
                "username": username,
                "password": password
            }
            async with self.session.post(f"{self.base_url}/auth/login", json=login_data) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    if data.get("success"):
                        self.token = data["data"]["access_token"]
                        print(f"✅ 登录成功: {data['data']['user_info']['username']}")
                        return True
                    else:
                        print(f"❌ 登录失败: {data.get('message', '未知错误')}")
                        return False
                else:
                    error_data = await resp.json()
                    print(f"❌ 登录请求失败: {error_data.get('detail', '未知错误')}")
                    return False
        except Exception as e:
            print(f"❌ 登录测试异常: {e}")
            return False
    
    async def test_channels(self):
        """测试通道列表（需要认证）"""
        print("🔍 测试通道列表...")
        if not self.token:
            print("⚠️  跳过通道列表测试（需要认证）")
            return True
        
        try:
            headers = {"Authorization": f"Bearer {self.token}"}
            async with self.session.get(f"{self.base_url}/channels", headers=headers) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    if data.get("success"):
                        total = data["data"]["total"]
                        print(f"✅ 通道列表: 共{total}个通道")
                        return True
                    else:
                        print(f"❌ 通道列表获取失败: {data.get('message', '未知错误')}")
                        return False
                else:
                    error_data = await resp.json()
                    print(f"❌ 通道列表请求失败: {error_data.get('detail', '未知错误')}")
                    return False
        except Exception as e:
            print(f"❌ 通道列表测试异常: {e}")
            return False
    
    async def test_websocket(self):
        """测试WebSocket连接"""
        print("🔍 测试WebSocket连接...")
        try:
            import websockets
            
            ws_url = f"ws://localhost:8000/api/v1/realtime/ws/general"
            if self.token:
                ws_url += f"?token={self.token}"
            
            async with websockets.connect(ws_url) as websocket:
                # 发送ping消息
                await websocket.send("ping")
                
                # 等待响应
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(response)
                
                if data.get("type") == "pong":
                    print("✅ WebSocket连接正常")
                    return True
                else:
                    print(f"❌ WebSocket响应异常: {data}")
                    return False
                    
        except ImportError:
            print("⚠️  跳过WebSocket测试（需要安装websockets库）")
            return True
        except Exception as e:
            print(f"❌ WebSocket测试异常: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始API功能测试...\n")
        
        tests = [
            ("健康检查", self.test_health_check()),
            ("登录功能", self.test_login()),
            ("系统状态", self.test_system_status()),
            ("通道列表", self.test_channels()),
            ("WebSocket", self.test_websocket()),
        ]
        
        results = []
        for test_name, test_coro in tests:
            try:
                result = await test_coro
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name}测试异常: {e}")
                results.append((test_name, False))
            print()  # 空行分隔
        
        # 输出测试结果
        print("📊 测试结果汇总:")
        print("-" * 40)
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name:15} {status}")
            if result:
                passed += 1
        
        print("-" * 40)
        print(f"总计: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！")
            return True
        else:
            print("⚠️  部分测试失败，请检查服务状态")
            return False


async def main():
    """主函数"""
    print("视频监控API测试工具")
    print("=" * 50)
    
    # 检查服务是否运行
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8000/health") as resp:
                if resp.status != 200:
                    print("❌ API服务未运行，请先启动服务")
                    return False
    except Exception:
        print("❌ 无法连接到API服务，请确认服务已启动")
        return False
    
    # 运行测试
    async with APITester() as tester:
        success = await tester.run_all_tests()
        return success


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 测试已取消")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试运行异常: {e}")
        sys.exit(1)
