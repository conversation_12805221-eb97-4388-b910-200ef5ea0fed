"""
配置管理模块

提供硬编码配置信息，不再依赖外部配置文件。
"""

import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class Settings:
    """配置管理类，提供硬编码的配置信息"""

    def __init__(self, resources_dir: Optional[str] = None):
        """
        初始化配置管理器

        Args:
            resources_dir: 资源文件目录路径（保留参数以兼容现有代码，但不再使用）
        """
        # 硬编码的配置信息
        self._hardcoded_config = {
            'baseinfo': {
                'ip': '**************',
                'port': '7282',
                'userName': 'haishi',
                'password': 'haishi@123'
            }
        }

        # 保留resources_dir以兼容SSL helper等其他模块
        if resources_dir is None:
            from pathlib import Path
            current_dir = Path(__file__).parent.parent.parent.parent
            self.resources_dir = current_dir / "resources"
        else:
            from pathlib import Path
            self.resources_dir = Path(resources_dir)

        logger.info(f"配置管理器初始化完成，使用硬编码配置")
    
    def _get_config_section(self, bundle_name: str) -> Dict[str, str]:
        """
        获取配置段

        Args:
            bundle_name: 配置段名称

        Returns:
            配置字典
        """
        if bundle_name in self._hardcoded_config:
            return self._hardcoded_config[bundle_name]
        else:
            logger.warning(f"配置段 '{bundle_name}' 不存在")
            return {}
    
    def get_string(self, bundle_name: str, key: str, default: Optional[str] = None) -> str:
        """
        获取字符串配置值

        Args:
            bundle_name: 配置段名称
            key: 配置键
            default: 默认值

        Returns:
            配置值
        """
        try:
            config_section = self._get_config_section(bundle_name)
            value = config_section.get(key, default)

            if value is None:
                raise KeyError(f"Configuration key '{key}' not found in '{bundle_name}'")

            return value.strip() if isinstance(value, str) else str(value)

        except Exception as e:
            logger.error(f"Error reading configuration {bundle_name}.{key}: {e}")
            if default is not None:
                return default
            raise
    
    def set_string(self, bundle_name: str, key: str, value: str) -> None:
        """
        设置字符串配置值（仅在内存中，不再保存到文件）

        Args:
            bundle_name: 配置段名称
            key: 配置键
            value: 配置值
        """
        try:
            if bundle_name not in self._hardcoded_config:
                self._hardcoded_config[bundle_name] = {}

            self._hardcoded_config[bundle_name][key] = value
            logger.debug(f"Updated configuration {bundle_name}.{key} = {value} (in memory only)")

        except Exception as e:
            logger.error(f"Error setting configuration {bundle_name}.{key}: {e}")
            raise
    
    def get_int(self, bundle_name: str, key: str, default: Optional[int] = None) -> int:
        """
        获取整数配置值
        
        Args:
            bundle_name: 配置文件名
            key: 配置键
            default: 默认值
            
        Returns:
            整数配置值
        """
        value_str = self.get_string(bundle_name, key, str(default) if default is not None else None)
        try:
            return int(value_str)
        except ValueError:
            if default is not None:
                return default
            raise ValueError(f"Configuration value '{value_str}' is not a valid integer")
    
    def get_bool(self, bundle_name: str, key: str, default: Optional[bool] = None) -> bool:
        """
        获取布尔配置值
        
        Args:
            bundle_name: 配置文件名
            key: 配置键
            default: 默认值
            
        Returns:
            布尔配置值
        """
        value_str = self.get_string(bundle_name, key, str(default).lower() if default is not None else None)
        return value_str.lower() in ('true', '1', 'yes', 'on')
    
    def reload_config(self, bundle_name: str) -> None:
        """
        重新加载指定的配置（硬编码配置无需重新加载）

        Args:
            bundle_name: 配置段名称
        """
        logger.debug(f"配置段 '{bundle_name}' 无需重新加载（硬编码配置）")

    def clear_cache(self) -> None:
        """清除所有配置缓存（硬编码配置无需清除缓存）"""
        logger.debug("硬编码配置无需清除缓存")


# 全局配置实例
_global_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """获取全局配置实例"""
    global _global_settings
    if _global_settings is None:
        _global_settings = Settings()
    return _global_settings


def init_settings(resources_dir: Optional[str] = None) -> Settings:
    """
    初始化全局配置实例
    
    Args:
        resources_dir: 资源目录路径
        
    Returns:
        配置实例
    """
    global _global_settings
    _global_settings = Settings(resources_dir)
    return _global_settings
