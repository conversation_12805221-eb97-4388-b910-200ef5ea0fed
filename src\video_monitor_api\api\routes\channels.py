"""
通道相关API路由

提供通道信息查询、管理等功能的API端点。
"""

import logging
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query

from ...models.api_models import ApiResponse, ChannelResponse, UserInfo
from ...core.monitor_service import MonitorService
from ...core.database_service import DatabaseService
from ...api.deps import get_current_user, get_database_service

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/channels", tags=["通道管理"])


def get_monitor_service(
    database_service: DatabaseService = Depends(get_database_service)
) -> MonitorService:
    """获取监控服务实例"""
    return MonitorService(database_service)


@router.get("", response_model=ApiResponse, summary="获取通道列表")
async def get_channels(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(100, ge=1, le=1000, description="每页大小"),
    status: Optional[str] = Query(None, description="状态过滤"),
    force_refresh: bool = Query(False, description="是否强制从监控系统刷新"),
    current_user: UserInfo = Depends(get_current_user),
    monitor_service: MonitorService = Depends(get_monitor_service)
):
    """
    获取通道列表接口
    
    - **page**: 页码，从1开始
    - **page_size**: 每页大小，最大1000
    - **status**: 状态过滤（online/offline/error/unknown）
    - **force_refresh**: 是否强制从监控系统刷新数据
    
    返回分页的通道列表
    """
    try:
        logger.info(f"获取通道列表请求: page={page}, page_size={page_size}, status={status}, force_refresh={force_refresh}")
        
        # 从JWT令牌中提取监控系统token
        from ...core.auth_service import AuthService
        auth_service = AuthService()
        token_payload = auth_service.verify_token(current_user.token)
        monitor_token = token_payload.get("monitor_token") if token_payload else None
        
        if not monitor_token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="监控系统令牌无效"
            )
        
        channels, total = await monitor_service.get_channels(
            token=monitor_token,
            page=page,
            page_size=page_size,
            force_refresh=force_refresh
        )
        
        # 如果有状态过滤，在内存中过滤
        if status:
            channels = [ch for ch in channels if ch.status == status]
            total = len(channels)
        
        response_data = ChannelResponse(
            channels=channels,
            total=total,
            page=page,
            page_size=page_size
        )
        
        return ApiResponse.success_response(
            data=response_data.model_dump(),
            message=f"获取通道列表成功，共{total}个通道"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取通道列表接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取通道列表服务异常"
        )


@router.get("/{channel_id}", response_model=ApiResponse, summary="获取通道详情")
async def get_channel_detail(
    channel_id: str,
    current_user: UserInfo = Depends(get_current_user),
    monitor_service: MonitorService = Depends(get_monitor_service)
):
    """
    获取通道详情接口
    
    - **channel_id**: 通道ID
    
    返回指定通道的详细信息
    """
    try:
        logger.info(f"获取通道详情请求: {channel_id}")
        
        channel = await monitor_service.get_channel_by_id(channel_id)
        
        if not channel:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"通道不存在: {channel_id}"
            )
        
        return ApiResponse.success_response(
            data=channel.model_dump(),
            message="获取通道详情成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取通道详情接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取通道详情服务异常"
        )


@router.post("/refresh", response_model=ApiResponse, summary="刷新通道数据")
async def refresh_channels(
    current_user: UserInfo = Depends(get_current_user),
    monitor_service: MonitorService = Depends(get_monitor_service)
):
    """
    刷新通道数据接口
    
    从监控系统重新获取最新的通道数据并更新到数据库
    """
    try:
        logger.info("刷新通道数据请求")
        
        # 从JWT令牌中提取监控系统token
        from ...core.auth_service import AuthService
        auth_service = AuthService()
        token_payload = auth_service.verify_token(current_user.token)
        monitor_token = token_payload.get("monitor_token") if token_payload else None
        
        if not monitor_token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="监控系统令牌无效"
            )
        
        # 强制刷新通道数据
        _, total = await monitor_service.get_channels(
            token=monitor_token,
            force_refresh=True
        )
        
        return ApiResponse.success_response(
            data={"refreshed_count": total},
            message=f"刷新通道数据成功，更新了{total}个通道"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刷新通道数据接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="刷新通道数据服务异常"
        )


@router.get("/statistics/summary", response_model=ApiResponse, summary="获取通道统计信息")
async def get_channel_statistics(
    current_user: UserInfo = Depends(get_current_user),
    database_service: DatabaseService = Depends(get_database_service)
):
    """
    获取通道统计信息接口
    
    返回通道的各种统计数据
    """
    try:
        logger.info("获取通道统计信息请求")
        
        stats = await database_service.get_statistics()
        
        return ApiResponse.success_response(
            data=stats,
            message="获取通道统计信息成功"
        )
        
    except Exception as e:
        logger.error(f"获取通道统计信息接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取通道统计信息服务异常"
        )
