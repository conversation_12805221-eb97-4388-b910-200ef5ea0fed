"""
监控URL相关API路由

提供监控URL生成、管理等功能的API端点。
"""

import logging
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status

from ...models.api_models import (
    ApiResponse, MonitorUrlRequest, MonitorUrlResponse, 
    BatchMonitorUrlRequest, UserInfo
)
from ...core.monitor_service import MonitorService
from ...core.database_service import DatabaseService
from ...core.auth_service import AuthService
from ...api.deps import get_current_user, get_database_service

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/monitor", tags=["监控URL"])


def get_monitor_service(
    database_service: DatabaseService = Depends(get_database_service)
) -> MonitorService:
    """获取监控服务实例"""
    return MonitorService(database_service)


@router.post("/urls", response_model=ApiResponse, summary="生成监控URL")
async def generate_monitor_url(
    request: MonitorUrlRequest,
    current_user: UserInfo = Depends(get_current_user),
    monitor_service: MonitorService = Depends(get_monitor_service)
):
    """
    生成监控URL接口
    
    - **channel_id**: 通道ID
    - **scheme**: 协议类型（RTSP/FLV_HTTP/HLS）
    - **sub_type**: 码流类型（0=主码流，1=子码流，2=第三码流）
    
    返回生成的监控URL信息
    """
    try:
        logger.info(f"生成监控URL请求: {request.channel_id}-{request.scheme}-{request.sub_type}")
        
        # 从JWT令牌中提取监控系统token
        auth_service = AuthService()
        token_payload = auth_service.verify_token(current_user.token)
        monitor_token = token_payload.get("monitor_token") if token_payload else None
        
        if not monitor_token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="监控系统令牌无效"
            )
        
        # 生成监控URL
        urls = await monitor_service.get_monitor_urls(
            token=monitor_token,
            channel_ids=[request.channel_id],
            schemes=[request.scheme],
            sub_types=[request.sub_type]
        )
        
        if not urls:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"无法生成监控URL: {request.channel_id}"
            )
        
        response_data = MonitorUrlResponse(
            urls=urls,
            total=len(urls)
        )
        
        return ApiResponse.success_response(
            data=response_data.model_dump(),
            message="生成监控URL成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成监控URL接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="生成监控URL服务异常"
        )


@router.post("/urls/batch", response_model=ApiResponse, summary="批量生成监控URL")
async def batch_generate_monitor_urls(
    request: BatchMonitorUrlRequest,
    current_user: UserInfo = Depends(get_current_user),
    monitor_service: MonitorService = Depends(get_monitor_service)
):
    """
    批量生成监控URL接口
    
    - **channel_ids**: 通道ID列表
    - **schemes**: 协议类型列表（RTSP/FLV_HTTP/HLS）
    - **sub_types**: 码流类型列表（0=主码流，1=子码流，2=第三码流）
    
    返回批量生成的监控URL信息
    """
    try:
        logger.info(f"批量生成监控URL请求: {len(request.channel_ids)}个通道")
        
        # 从JWT令牌中提取监控系统token
        auth_service = AuthService()
        token_payload = auth_service.verify_token(current_user.token)
        monitor_token = token_payload.get("monitor_token") if token_payload else None
        
        if not monitor_token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="监控系统令牌无效"
            )
        
        # 批量生成监控URL
        urls = await monitor_service.get_monitor_urls(
            token=monitor_token,
            channel_ids=request.channel_ids,
            schemes=request.schemes,
            sub_types=request.sub_types
        )
        
        response_data = MonitorUrlResponse(
            urls=urls,
            total=len(urls)
        )
        
        return ApiResponse.success_response(
            data=response_data.model_dump(),
            message=f"批量生成监控URL成功，共生成{len(urls)}个URL"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量生成监控URL接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量生成监控URL服务异常"
        )


@router.get("/urls/{channel_id}", response_model=ApiResponse, summary="获取通道的监控URL")
async def get_channel_monitor_urls(
    channel_id: str,
    current_user: UserInfo = Depends(get_current_user),
    database_service: DatabaseService = Depends(get_database_service)
):
    """
    获取指定通道的监控URL列表
    
    - **channel_id**: 通道ID
    
    返回该通道的所有监控URL
    """
    try:
        logger.info(f"获取通道监控URL请求: {channel_id}")
        
        urls = await database_service.get_monitor_urls(channel_id=channel_id)
        
        response_data = MonitorUrlResponse(
            urls=urls,
            total=len(urls)
        )
        
        return ApiResponse.success_response(
            data=response_data.model_dump(),
            message=f"获取通道监控URL成功，共{len(urls)}个URL"
        )
        
    except Exception as e:
        logger.error(f"获取通道监控URL接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取通道监控URL服务异常"
        )


@router.get("/urls", response_model=ApiResponse, summary="获取所有监控URL")
async def get_all_monitor_urls(
    current_user: UserInfo = Depends(get_current_user),
    database_service: DatabaseService = Depends(get_database_service)
):
    """
    获取所有监控URL列表
    
    返回系统中所有的监控URL
    """
    try:
        logger.info("获取所有监控URL请求")
        
        urls = await database_service.get_monitor_urls()
        
        response_data = MonitorUrlResponse(
            urls=urls,
            total=len(urls)
        )
        
        return ApiResponse.success_response(
            data=response_data.model_dump(),
            message=f"获取所有监控URL成功，共{len(urls)}个URL"
        )
        
    except Exception as e:
        logger.error(f"获取所有监控URL接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取所有监控URL服务异常"
        )


@router.post("/start", response_model=ApiResponse, summary="启动监控任务")
async def start_monitoring(
    current_user: UserInfo = Depends(get_current_user),
    monitor_service: MonitorService = Depends(get_monitor_service)
):
    """
    启动监控任务接口
    
    启动后台监控任务，定期刷新通道数据和系统状态
    """
    try:
        logger.info("启动监控任务请求")
        
        # 从JWT令牌中提取监控系统token
        auth_service = AuthService()
        token_payload = auth_service.verify_token(current_user.token)
        monitor_token = token_payload.get("monitor_token") if token_payload else None
        
        if not monitor_token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="监控系统令牌无效"
            )
        
        success = await monitor_service.start_monitoring(monitor_token)
        
        if success:
            return ApiResponse.success_response(
                message="监控任务启动成功"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="监控任务启动失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动监控任务接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="启动监控任务服务异常"
        )


@router.post("/stop", response_model=ApiResponse, summary="停止监控任务")
async def stop_monitoring(
    current_user: UserInfo = Depends(get_current_user),
    monitor_service: MonitorService = Depends(get_monitor_service)
):
    """
    停止监控任务接口
    
    停止后台监控任务
    """
    try:
        logger.info("停止监控任务请求")
        
        success = await monitor_service.stop_monitoring()
        
        if success:
            return ApiResponse.success_response(
                message="监控任务停止成功"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="监控任务停止失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止监控任务接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="停止监控任务服务异常"
        )


@router.get("/status", response_model=ApiResponse, summary="获取监控状态")
async def get_monitoring_status(
    current_user: UserInfo = Depends(get_current_user),
    monitor_service: MonitorService = Depends(get_monitor_service)
):
    """
    获取监控状态接口
    
    返回当前监控任务的运行状态
    """
    try:
        logger.info("获取监控状态请求")
        
        status_info = monitor_service.get_monitoring_status()
        
        return ApiResponse.success_response(
            data=status_info,
            message="获取监控状态成功"
        )
        
    except Exception as e:
        logger.error(f"获取监控状态接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取监控状态服务异常"
        )
