"""
系统管理相关API路由

提供系统状态、健康检查、配置管理等功能的API端点。
"""

import logging
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status

from ...models.api_models import ApiResponse, SystemStatus, HealthCheckResponse, UserInfo
from ...core.database_service import DatabaseService
from ...core.websocket_manager import websocket_manager
from ...config.settings import get_settings
from ...config.database import get_supabase_client, check_database_health
from ...api.deps import get_current_user, get_database_service
# 版本号常量
__version__ = "2.0.0"

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/system", tags=["系统管理"])

# 系统启动时间
_startup_time = datetime.now()


@router.get("/status", response_model=ApiResponse, summary="获取系统状态")
async def get_system_status(
    current_user: UserInfo = Depends(get_current_user),
    database_service: DatabaseService = Depends(get_database_service)
):
    """
    获取系统状态接口
    
    返回系统的详细运行状态信息
    """
    try:
        logger.info("获取系统状态请求")
        
        settings = get_settings()
        
        # 获取数据库统计信息
        stats = await database_service.get_statistics()
        
        # 获取WebSocket连接统计
        ws_stats = websocket_manager.get_connection_stats()
        
        # 计算运行时间
        uptime_seconds = int((datetime.now() - _startup_time).total_seconds())
        
        # 检查数据库状态
        try:
            db_health = await check_database_health()
            database_status = "healthy" if db_health else "unhealthy"
        except Exception:
            database_status = "error"
        
        # 检查监控系统状态（简单检查）
        monitor_system_status = "unknown"  # 这里可以添加实际的监控系统状态检查
        
        system_status = SystemStatus(
            service_name="Video Monitor API",
            version=__version__,
            status="running",
            uptime=uptime_seconds,
            database_status=database_status,
            monitor_system_status=monitor_system_status,
            active_connections=ws_stats.get("total_connections", 0),
            total_channels=stats.get("total_channels", 0)
        )
        
        return ApiResponse.success_response(
            data=system_status.model_dump(),
            message="获取系统状态成功"
        )
        
    except Exception as e:
        logger.error(f"获取系统状态接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统状态服务异常"
        )


@router.get("/health", response_model=HealthCheckResponse, summary="健康检查")
async def health_check():
    """
    健康检查接口
    
    返回系统各组件的健康状态，用于负载均衡器和监控系统
    """
    try:
        checks = {}
        overall_status = "healthy"
        
        # 检查数据库连接
        try:
            db_health = await check_database_health()
            checks["database"] = "healthy" if db_health else "unhealthy"
            if not db_health:
                overall_status = "unhealthy"
        except Exception as e:
            checks["database"] = f"error: {str(e)}"
            overall_status = "unhealthy"
        
        # 检查WebSocket管理器
        try:
            ws_stats = websocket_manager.get_connection_stats()
            checks["websocket"] = "healthy"
        except Exception as e:
            checks["websocket"] = f"error: {str(e)}"
            overall_status = "unhealthy"
        
        # 检查配置
        try:
            settings = get_settings()
            checks["configuration"] = "healthy"
        except Exception as e:
            checks["configuration"] = f"error: {str(e)}"
            overall_status = "unhealthy"
        
        return HealthCheckResponse(
            status=overall_status,
            checks=checks
        )
        
    except Exception as e:
        logger.error(f"健康检查异常: {e}")
        return HealthCheckResponse(
            status="error",
            checks={"system": f"error: {str(e)}"}
        )


@router.get("/config", response_model=ApiResponse, summary="获取系统配置")
async def get_system_config(
    current_user: UserInfo = Depends(get_current_user)
):
    """
    获取系统配置接口
    
    返回系统的配置信息（敏感信息已脱敏）
    """
    try:
        logger.info("获取系统配置请求")
        
        settings = get_settings()
        
        # 构建配置信息（脱敏处理）
        config_info = {
            "service_name": "Video Monitor API",
            "version": __version__,
            "environment": settings.ENVIRONMENT,
            "debug": settings.DEBUG,
            "log_level": settings.LOG_LEVEL,
            "monitor_system": {
                "ip": settings.MONITOR_SYSTEM_IP,
                "port": settings.MONITOR_SYSTEM_PORT,
                "username": settings.MONITOR_USERNAME  # 用户名可以显示
            },
            "database": {
                "url": settings.SUPABASE_URL,
                "realtime_enabled": True
            },
            "websocket": {
                "enabled": True,
                "heartbeat_interval": 30
            },
            "monitoring": {
                "refresh_interval": settings.MONITOR_REFRESH_INTERVAL
            }
        }
        
        return ApiResponse.success_response(
            data=config_info,
            message="获取系统配置成功"
        )
        
    except Exception as e:
        logger.error(f"获取系统配置接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统配置服务异常"
        )


@router.post("/cleanup", response_model=ApiResponse, summary="清理系统数据")
async def cleanup_system_data(
    current_user: UserInfo = Depends(get_current_user),
    database_service: DatabaseService = Depends(get_database_service)
):
    """
    清理系统数据接口
    
    清理过期的会话、日志等临时数据
    """
    try:
        logger.info("清理系统数据请求")
        
        # 清理过期会话
        cleaned_sessions = await database_service.cleanup_expired_sessions()
        
        cleanup_result = {
            "cleaned_sessions": cleaned_sessions,
            "cleanup_time": datetime.now().isoformat()
        }
        
        return ApiResponse.success_response(
            data=cleanup_result,
            message=f"系统数据清理完成，清理了{cleaned_sessions}个过期会话"
        )
        
    except Exception as e:
        logger.error(f"清理系统数据接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="清理系统数据服务异常"
        )


@router.get("/logs", response_model=ApiResponse, summary="获取系统日志")
async def get_system_logs(
    lines: int = 100,
    current_user: UserInfo = Depends(get_current_user)
):
    """
    获取系统日志接口
    
    - **lines**: 返回的日志行数，默认100行
    
    返回最近的系统日志
    """
    try:
        logger.info(f"获取系统日志请求: {lines}行")
        
        settings = get_settings()
        log_file = settings.LOG_FILE
        
        try:
            # 读取日志文件的最后几行
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                
            log_content = ''.join(recent_lines)
            
            return ApiResponse.success_response(
                data={
                    "log_file": log_file,
                    "lines_returned": len(recent_lines),
                    "content": log_content
                },
                message=f"获取系统日志成功，返回{len(recent_lines)}行"
            )
            
        except FileNotFoundError:
            return ApiResponse.success_response(
                data={
                    "log_file": log_file,
                    "lines_returned": 0,
                    "content": "日志文件不存在"
                },
                message="日志文件不存在"
            )
        
    except Exception as e:
        logger.error(f"获取系统日志接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统日志服务异常"
        )
