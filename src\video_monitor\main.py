"""
主程序模块

整合所有模块，实现完整的视频监控通道管理工作流程。
"""

import logging
import sys
import time
from typing import List, Optional, Dict, Any
from pathlib import Path

# 支持直接运行和模块运行
if __name__ == "__main__" and __package__ is None:
    # 直接运行时，添加项目根目录到sys.path
    current_dir = Path(__file__).parent.parent.parent
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))

    from video_monitor.config.settings import Settings, init_settings
    from video_monitor.utils.ssl_helper import SS<PERSON><PERSON>elper, init_ssl_helper
    from video_monitor.utils.crypto import CryptoUtils
    from video_monitor.api.client import ApiClient
    from video_monitor.api.endpoints import ApiEndpoints, create_endpoints
    from video_monitor.auth.login import LoginManager
    from video_monitor.auth.session import SessionManager
    from video_monitor.business.monitor_service import MonitorService
    from video_monitor.models.data_models import ChannelInfo, MonitorUrlInfo, Constants
else:
    # 模块运行时，使用相对导入
    from .config.settings import Settings, init_settings
    from .utils.ssl_helper import SSLHelper, init_ssl_helper
    from .utils.crypto import CryptoUtils
    from .api.client import ApiClient
    from .api.endpoints import ApiEndpoints, create_endpoints
    from .auth.login import LoginManager
    from .auth.session import SessionManager
    from .business.monitor_service import MonitorService
    from .models.data_models import ChannelInfo, MonitorUrlInfo, Constants

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('video_monitor.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)


class RealMonitorChannelManager:
    """
    实时监控通道管理器
    
    Python版本的RealMonitorChannelManager，与Java版本功能完全一致。
    """
    
    def __init__(self, resources_dir: Optional[str] = None):
        """
        初始化通道管理器
        
        Args:
            resources_dir: 资源文件目录路径
        """
        try:
            logger.info("🚀 初始化实时监控通道管理器...")
            
            # 初始化配置管理
            self.settings = init_settings(resources_dir)
            
            # 初始化SSL助手
            self.ssl_helper = init_ssl_helper(resources_dir)
            
            # 创建API客户端
            self.api_client = ApiClient(ssl_helper=self.ssl_helper)
            
            # 创建API端点管理器
            ip = self.settings.get_string('baseinfo', 'ip')
            port = self.settings.get_int('baseinfo', 'port')
            self.endpoints = create_endpoints(ip, port, use_https=True)
            
            # 创建登录管理器
            self.login_manager = LoginManager(
                api_client=self.api_client,
                endpoints=self.endpoints,
                settings=self.settings
            )
            
            # 创建会话管理器
            self.session_manager = SessionManager(
                api_client=self.api_client,
                endpoints=self.endpoints
            )
            
            # 监控服务（登录后初始化）
            self.monitor_service: Optional[MonitorService] = None
            
            # 状态变量
            self._token: Optional[str] = None
            self._is_logged_in = False
            
            logger.info("✅ 通道管理器初始化完成")
            
        except Exception as e:
            logger.error(f"初始化通道管理器失败: {e}")
            raise
    
    def perform_secure_login(self) -> bool:
        """
        执行安全SSL证书登录
        
        Returns:
            登录是否成功
        """
        try:
            logger.info("🔐 开始执行安全SSL证书登录...")
            
            # 验证登录凭据
            if not self.login_manager.validate_credentials():
                logger.error("登录凭据验证失败")
                return False
            
            # 尝试使用保存的token
            saved_token = self.login_manager.get_saved_token()
            if saved_token:
                logger.info("发现已保存的token，尝试验证...")
                if self._validate_token(saved_token):
                    self._token = saved_token
                    self._is_logged_in = True
                    self._initialize_monitor_service()
                    logger.info("✅ 使用已保存的token登录成功")
                    return True
                else:
                    logger.info("已保存的token无效，执行重新登录")
                    self.login_manager.clear_saved_token()
            
            # 执行登录流程
            success, token, error_msg = self.login_manager.perform_secure_login()
            
            if success and token:
                self._token = token
                self._is_logged_in = True
                self._initialize_monitor_service()
                
                # 启动会话管理
                self.session_manager.start_session(
                    token=token,
                    on_session_expired=self._on_session_expired
                )
                
                logger.info("✅ 登录成功，会话管理已启动")
                return True
            else:
                logger.error(f"登录失败: {error_msg}")
                return False
                
        except Exception as e:
            logger.error(f"登录过程异常: {e}")
            return False
    
    def _validate_token(self, token: str) -> bool:
        """
        验证token是否有效
        
        Args:
            token: 认证令牌
            
        Returns:
            token是否有效
        """
        try:
            # 尝试发送一个简单的API请求来验证token
            url = self.endpoints.get_keep_alive_url()
            response = self.api_client.post(url, data={"token": token}, token=token)
            return response.success
        except Exception:
            return False
    
    def _initialize_monitor_service(self) -> None:
        """初始化监控服务"""
        if self._token:
            self.monitor_service = MonitorService(
                api_client=self.api_client,
                endpoints=self.endpoints,
                token=self._token
            )
    
    def _on_session_expired(self) -> None:
        """会话过期回调"""
        logger.warning("⚠️ 会话已过期，清理状态")
        self._token = None
        self._is_logged_in = False
        self.monitor_service = None
        self.login_manager.clear_saved_token()
    
    def get_sub(self) -> List[ChannelInfo]:
        """
        获取所有通道信息
        
        Returns:
            通道信息列表
        """
        try:
            if not self._is_logged_in or not self.monitor_service:
                logger.error("未登录或监控服务未初始化")
                return []
            
            logger.info("📡 开始获取所有通道信息...")
            channels = self.monitor_service.get_sub()
            
            logger.info(f"✅ 成功获取 {len(channels)} 个通道")
            return channels
            
        except Exception as e:
            logger.error(f"获取通道信息异常: {e}")
            return []
    
    def get_real_monitor_url(self, channel_id: str, scheme: str = "RTSP", 
                           sub_type: int = 0) -> Optional[MonitorUrlInfo]:
        """
        获取实时监控URI
        
        Args:
            channel_id: 通道ID
            scheme: 协议类型
            sub_type: 码流类型
            
        Returns:
            监控URL信息
        """
        try:
            if not self._is_logged_in or not self.monitor_service:
                logger.error("未登录或监控服务未初始化")
                return None
            
            logger.info(f"🎥 获取监控URI: {channel_id} ({scheme})")
            monitor_url = self.monitor_service.get_real_monitor_url(
                channel_id=channel_id,
                scheme=scheme,
                sub_type=sub_type
            )
            
            if monitor_url:
                logger.info(f"✅ 成功获取监控URI: {monitor_url.url}")
            else:
                logger.warning(f"未能获取通道 {channel_id} 的监控URI")
            
            return monitor_url
            
        except Exception as e:
            logger.error(f"获取监控URI异常: {e}")
            return None
    
    def get_all_monitor_urls(self, schemes: List[str] = None, 
                           sub_types: List[int] = None) -> List[MonitorUrlInfo]:
        """
        获取所有通道的监控URI
        
        Args:
            schemes: 协议类型列表，默认为["RTSP"]
            sub_types: 码流类型列表，默认为[0]
            
        Returns:
            监控URL信息列表
        """
        try:
            if schemes is None:
                schemes = ["RTSP"]
            if sub_types is None:
                sub_types = [0]
            
            logger.info(f"🎬 开始获取所有通道的监控URI...")
            
            # 获取所有通道
            channels = self.get_sub()
            if not channels:
                logger.warning("没有找到任何通道")
                return []
            
            monitor_urls = []
            total_combinations = len(channels) * len(schemes) * len(sub_types)
            current_count = 0
            
            for channel in channels:
                for scheme in schemes:
                    for sub_type in sub_types:
                        current_count += 1
                        logger.info(f"进度: {current_count}/{total_combinations} - 处理通道 {channel.channel_name}")
                        
                        monitor_url = self.get_real_monitor_url(
                            channel_id=channel.channel_id,
                            scheme=scheme,
                            sub_type=sub_type
                        )
                        
                        if monitor_url:
                            # 更新通道和组织信息
                            monitor_url.channel_name = channel.channel_name
                            monitor_url.org_name = channel.org_name
                            monitor_urls.append(monitor_url)
                        
                        # 添加小延迟，避免请求过于频繁
                        time.sleep(0.1)
            
            logger.info(f"✅ 成功获取 {len(monitor_urls)} 个监控URI")
            return monitor_urls
            
        except Exception as e:
            logger.error(f"获取所有监控URI异常: {e}")
            return []
    
    def logout(self) -> bool:
        """
        登出并清理资源
        
        Returns:
            登出是否成功
        """
        try:
            logger.info("🚪 开始登出...")
            
            success = True
            
            # 停止会话管理
            if self.session_manager.is_session_active():
                session_success = self.session_manager.stop_session(perform_logout=True)
                if not session_success:
                    success = False
            
            # 清理状态
            self._token = None
            self._is_logged_in = False
            self.monitor_service = None
            
            # 关闭API客户端
            self.api_client.close()
            
            logger.info("✅ 登出完成")
            return success
            
        except Exception as e:
            logger.error(f"登出异常: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取系统状态信息
        
        Returns:
            状态信息字典
        """
        return {
            'logged_in': self._is_logged_in,
            'has_token': bool(self._token),
            'session_info': self.session_manager.get_session_info(),
            'login_info': self.login_manager.get_login_info()
        }
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.logout()


def main():
    """主函数，演示完整的工作流程"""
    try:
        logger.info("🎯 启动视频监控通道管理器演示程序")
        
        # 创建通道管理器
        with RealMonitorChannelManager() as manager:
            # 执行登录
            if not manager.perform_secure_login():
                logger.error("登录失败，程序退出")
                return 1
            
            # 获取所有通道信息
            channels = manager.get_sub()
            if not channels:
                logger.warning("没有找到任何通道")
                return 0
            
            # 显示通道信息
            logger.info(f"📋 通道列表 ({len(channels)} 个):")
            for i, channel in enumerate(channels[:5], 1):  # 只显示前5个
                logger.info(f"  {i}. {channel}")
            
            if len(channels) > 5:
                logger.info(f"  ... 还有 {len(channels) - 5} 个通道")
            
            # 获取第一个通道的监控URI（如果有通道的话）
            if channels:
                first_channel = channels[0]
                monitor_url = manager.get_real_monitor_url(
                    channel_id=first_channel.channel_id,
                    scheme="RTSP",
                    sub_type=0
                )
                
                if monitor_url:
                    logger.info(f"🎥 示例监控URI: {monitor_url}")
            
            logger.info("✅ 演示程序执行完成")
            return 0
            
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
        return 0
    except Exception as e:
        logger.error(f"程序执行异常: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
