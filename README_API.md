# 视频监控API服务

基于FastAPI的视频监控后端API服务，集成Supabase数据库和实时功能。

## 功能特性

- 🔐 **用户认证**: JWT令牌认证，集成原监控系统
- 📺 **通道管理**: 通道信息查询、状态监控
- 🎥 **监控URL**: 多协议视频流URL生成（RTSP/FLV_HTTP/HLS）
- ⚡ **实时功能**: WebSocket实时数据推送
- 📊 **系统管理**: 健康检查、状态监控、日志管理
- 🗄️ **数据持久化**: Supabase PostgreSQL数据库
- 📖 **API文档**: 自动生成的OpenAPI文档

## 技术栈

- **FastAPI**: 现代、快速的Web框架
- **Supabase**: PostgreSQL数据库和实时功能
- **WebSocket**: 实时双向通信
- **JWT**: 安全的用户认证
- **Pydantic**: 数据验证和序列化
- **Uvicorn**: ASGI服务器

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt

# 或使用Poetry
poetry install
```

### 2. 环境配置

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

### 3. 数据库初始化

```bash
# 在Supabase中执行SQL脚本
psql -h your-supabase-host -U postgres -d postgres -f database/supabase_schema.sql
```

### 4. 启动服务

```bash
# 使用启动脚本
python run_api.py

# 或直接使用uvicorn
uvicorn src.video_monitor_api.main:app --host 0.0.0.0 --port 8000 --reload
```

### 5. 访问API文档

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## API端点

### 认证相关 `/api/v1/auth`

- `POST /login` - 用户登录
- `POST /logout` - 用户登出
- `POST /refresh` - 刷新令牌
- `GET /me` - 获取当前用户信息
- `GET /status` - 获取认证状态

### 通道管理 `/api/v1/channels`

- `GET /` - 获取通道列表
- `GET /{channel_id}` - 获取通道详情
- `POST /refresh` - 刷新通道数据
- `GET /statistics/summary` - 获取通道统计

### 监控URL `/api/v1/monitor`

- `POST /urls` - 生成监控URL
- `POST /urls/batch` - 批量生成监控URL
- `GET /urls/{channel_id}` - 获取通道监控URL
- `GET /urls` - 获取所有监控URL
- `POST /start` - 启动监控任务
- `POST /stop` - 停止监控任务
- `GET /status` - 获取监控状态

### 实时功能 `/api/v1/realtime`

- `WebSocket /ws/{connection_type}` - WebSocket连接
- `GET /alerts` - 获取未解决告警
- `POST /test-broadcast` - 测试广播消息
- `GET /connections` - 获取连接统计

### 系统管理 `/api/v1/system`

- `GET /status` - 获取系统状态
- `GET /health` - 健康检查
- `GET /config` - 获取系统配置
- `POST /cleanup` - 清理系统数据
- `GET /logs` - 获取系统日志

## WebSocket使用

### 连接类型

- `monitor` - 监控数据推送
- `alerts` - 告警信息推送
- `general` - 通用消息推送

### 连接示例

```javascript
// 建立WebSocket连接
const ws = new WebSocket('ws://localhost:8000/api/v1/realtime/ws/monitor?token=your-jwt-token');

// 监听消息
ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('收到消息:', data);
};

// 发送心跳
setInterval(() => {
    ws.send('ping');
}, 30000);
```

## 数据库结构

### 主要表

- `channels` - 通道信息
- `monitor_urls` - 监控URL记录
- `monitor_sessions` - 监控会话
- `system_alerts` - 系统告警

详细结构请参考 `database/supabase_schema.sql`

## 部署说明

### Docker部署

```bash
# 构建镜像
docker build -t video-monitor-api .

# 运行容器
docker run -d -p 8000:8000 --env-file .env video-monitor-api
```

### 生产环境配置

1. 设置环境变量 `ENVIRONMENT=production`
2. 配置反向代理（Nginx）
3. 启用HTTPS
4. 配置日志轮转
5. 设置监控和告警

## 开发指南

### 项目结构

```
src/video_monitor_api/
├── api/                 # API路由
│   ├── routes/         # 路由定义
│   └── deps.py         # 依赖注入
├── core/               # 核心服务
├── models/             # 数据模型
├── config/             # 配置管理
├── utils/              # 工具函数
└── main.py             # 应用入口
```

### 添加新功能

1. 在 `models/` 中定义数据模型
2. 在 `core/` 中实现业务逻辑
3. 在 `api/routes/` 中添加API端点
4. 更新数据库schema（如需要）
5. 编写测试用例

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查Supabase配置
   - 确认网络连接
   - 验证认证信息

2. **JWT认证失败**
   - 检查SECRET_KEY配置
   - 确认令牌格式
   - 验证过期时间

3. **WebSocket连接断开**
   - 检查网络稳定性
   - 确认心跳机制
   - 查看服务器日志

### 日志查看

```bash
# 查看应用日志
tail -f logs/video_monitor_api.log

# 查看系统日志
journalctl -u video-monitor-api -f
```

## 许可证

本项目采用 MIT 许可证。
