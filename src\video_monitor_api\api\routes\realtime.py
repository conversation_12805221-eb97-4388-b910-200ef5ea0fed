"""
实时功能相关API路由

提供WebSocket连接、实时数据推送等功能的API端点。
"""

import logging
from typing import Optional
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, status, Query

from ...models.api_models import ApiResponse, UserInfo
from ...core.websocket_manager import websocket_manager
from ...core.database_service import DatabaseService
from ...core.auth_service import AuthService
from ...api.deps import get_current_user, get_database_service

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/realtime", tags=["实时功能"])


@router.websocket("/ws/{connection_type}")
async def websocket_endpoint(
    websocket: WebSocket,
    connection_type: str,
    token: Optional[str] = Query(None, description="JWT认证令牌")
):
    """
    WebSocket连接端点
    
    - **connection_type**: 连接类型（monitor/alerts/general）
    - **token**: JWT认证令牌（通过查询参数传递）
    
    建立WebSocket连接用于实时数据推送
    """
    try:
        # 验证令牌
        user_info = None
        if token:
            auth_service = AuthService()
            user_info = await auth_service.get_current_user_info(token)
            if not user_info:
                await websocket.close(code=4001, reason="Invalid token")
                return
        
        # 建立连接
        metadata = {"user_info": user_info.model_dump() if user_info else None}
        await websocket_manager.connect(websocket, connection_type, metadata)
        
        logger.info(f"WebSocket连接建立: {connection_type}, 用户: {user_info.username if user_info else 'Anonymous'}")
        
        try:
            # 保持连接并处理消息
            while True:
                # 接收客户端消息
                data = await websocket.receive_text()
                logger.debug(f"收到WebSocket消息: {data}")
                
                # 这里可以处理客户端发送的消息
                # 例如：心跳响应、订阅特定数据等
                if data == "ping":
                    await websocket_manager.send_personal_message(websocket, {
                        "type": "pong",
                        "data": "pong"
                    })
                
        except WebSocketDisconnect:
            logger.info(f"WebSocket连接断开: {connection_type}")
        except Exception as e:
            logger.error(f"WebSocket连接异常: {e}")
        finally:
            await websocket_manager.disconnect(websocket, connection_type)
            
    except Exception as e:
        logger.error(f"WebSocket端点异常: {e}")
        try:
            await websocket.close(code=4000, reason="Server error")
        except:
            pass


@router.get("/alerts", response_model=ApiResponse, summary="获取未解决的告警")
async def get_unresolved_alerts(
    current_user: UserInfo = Depends(get_current_user),
    database_service: DatabaseService = Depends(get_database_service)
):
    """
    获取未解决的告警列表
    
    返回当前系统中所有未解决的告警信息
    """
    try:
        logger.info("获取未解决告警请求")
        
        alerts = await database_service.get_unresolved_alerts()
        
        return ApiResponse.success_response(
            data={"alerts": [alert.model_dump() for alert in alerts]},
            message=f"获取未解决告警成功，共{len(alerts)}个告警"
        )
        
    except Exception as e:
        logger.error(f"获取未解决告警接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取未解决告警服务异常"
        )


@router.post("/test-broadcast", response_model=ApiResponse, summary="测试广播消息")
async def test_broadcast(
    message: str = Query(..., description="测试消息内容"),
    broadcast_type: str = Query("general", description="广播类型"),
    current_user: UserInfo = Depends(get_current_user)
):
    """
    测试广播消息接口
    
    - **message**: 测试消息内容
    - **broadcast_type**: 广播类型（monitor/alerts/general）
    
    用于测试WebSocket广播功能
    """
    try:
        logger.info(f"测试广播消息: {message}")
        
        await websocket_manager.broadcast_to_type(broadcast_type, {
            "type": "test_message",
            "data": {
                "message": message,
                "sender": current_user.username,
                "broadcast_type": broadcast_type
            }
        })
        
        return ApiResponse.success_response(
            message=f"测试广播消息发送成功: {broadcast_type}"
        )
        
    except Exception as e:
        logger.error(f"测试广播消息接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="测试广播消息服务异常"
        )


@router.get("/connections", response_model=ApiResponse, summary="获取连接统计")
async def get_connection_stats(
    current_user: UserInfo = Depends(get_current_user)
):
    """
    获取WebSocket连接统计信息
    
    返回当前活跃的WebSocket连接统计
    """
    try:
        logger.info("获取连接统计请求")
        
        stats = websocket_manager.get_connection_stats()
        
        return ApiResponse.success_response(
            data=stats,
            message="获取连接统计成功"
        )
        
    except Exception as e:
        logger.error(f"获取连接统计接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取连接统计服务异常"
        )
