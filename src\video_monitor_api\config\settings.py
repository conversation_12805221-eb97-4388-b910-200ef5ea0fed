"""
应用配置管理

使用Pydantic Settings管理应用配置，支持环境变量和默认值。
"""

import os
from typing import List, Optional
from functools import lru_cache

from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基本配置
    APP_NAME: str = "视频监控API服务"
    VERSION: str = "2.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        env="ALLOWED_ORIGINS"
    )
    
    # Supabase配置
    SUPABASE_URL: str = Field(
        default="http://*************:8000",
        env="SUPABASE_URL"
    )
    SUPABASE_ANON_KEY: str = Field(
        default="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE",
        env="SUPABASE_ANON_KEY"
    )
    SUPABASE_SERVICE_KEY: Optional[str] = Field(default=None, env="SUPABASE_SERVICE_KEY")
    
    # JWT配置
    SECRET_KEY: str = Field(
        default="your-secret-key-change-in-production",
        env="SECRET_KEY"
    )
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 视频监控系统配置（从原有配置迁移）
    MONITOR_SYSTEM_IP: str = Field(default="**************", env="MONITOR_SYSTEM_IP")
    MONITOR_SYSTEM_PORT: int = Field(default=7282, env="MONITOR_SYSTEM_PORT")
    MONITOR_USERNAME: str = Field(default="haishi", env="MONITOR_USERNAME")
    MONITOR_PASSWORD: str = Field(default="haishi@123", env="MONITOR_PASSWORD")
    
    # SSL证书配置
    SSL_CERT_PATH: Optional[str] = Field(default=None, env="SSL_CERT_PATH")
    SSL_KEY_PATH: Optional[str] = Field(default=None, env="SSL_KEY_PATH")
    SSL_ROOT_CHAIN_PATH: Optional[str] = Field(default=None, env="SSL_ROOT_CHAIN_PATH")
    
    # 会话管理配置
    SESSION_KEEP_ALIVE_INTERVAL: int = 110  # 秒
    SESSION_TIMEOUT: int = 3600  # 秒
    
    # WebSocket配置
    WS_HEARTBEAT_INTERVAL: int = 30  # 秒
    WS_MAX_CONNECTIONS: int = 100
    
    # 视频流配置
    STREAM_BUFFER_SIZE: int = 8192
    STREAM_TIMEOUT: int = 30
    SUPPORTED_SCHEMES: List[str] = ["RTSP", "FLV_HTTP", "HLS"]
    
    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE: str = Field(default="video_monitor_api.log", env="LOG_FILE")
    
    # 资源文件路径
    RESOURCES_DIR: str = Field(default="resources", env="RESOURCES_DIR")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
    
    def get_resources_path(self) -> str:
        """获取资源文件目录的绝对路径"""
        if os.path.isabs(self.RESOURCES_DIR):
            return self.RESOURCES_DIR
        
        # 相对于项目根目录
        from pathlib import Path
        project_root = Path(__file__).parent.parent.parent.parent
        return str(project_root / self.RESOURCES_DIR)
    
    def get_ssl_cert_path(self) -> Optional[str]:
        """获取SSL证书文件路径"""
        if self.SSL_CERT_PATH:
            return self.SSL_CERT_PATH
        
        # 默认路径
        resources_path = self.get_resources_path()
        cert_path = os.path.join(resources_path, "key.cer")
        return cert_path if os.path.exists(cert_path) else None
    
    def get_ssl_root_chain_path(self) -> Optional[str]:
        """获取SSL根证书链文件路径"""
        if self.SSL_ROOT_CHAIN_PATH:
            return self.SSL_ROOT_CHAIN_PATH
        
        # 默认路径
        resources_path = self.get_resources_path()
        chain_path = os.path.join(resources_path, "rootChain.crt")
        return chain_path if os.path.exists(chain_path) else None


@lru_cache()
def get_settings() -> Settings:
    """获取应用配置实例（单例模式）"""
    return Settings()
