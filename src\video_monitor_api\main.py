"""
FastAPI应用主入口

创建和配置FastAPI应用实例，注册路由和中间件。
"""

import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from .config.settings import get_settings
from .config.database import init_database
from .api import auth, channels, monitor, realtime, system
from .core.websocket_manager import websocket_manager
from .utils.logger import setup_logging

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("🚀 启动视频监控API服务...")
    
    # 初始化数据库连接
    await init_database()
    
    # 初始化WebSocket管理器
    await websocket_manager.initialize()
    
    logger.info("✅ 服务启动完成")
    
    yield
    
    # 关闭时清理
    logger.info("🛑 关闭视频监控API服务...")
    
    # 清理WebSocket连接
    await websocket_manager.cleanup()
    
    logger.info("✅ 服务关闭完成")


# 创建FastAPI应用
app = FastAPI(
    title="视频监控API服务",
    description="基于FastAPI的视频监控后端API服务，集成Supabase数据库和实时功能",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS
settings = get_settings()
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "message": "服务器遇到了一个错误，请稍后重试",
            "detail": str(exc) if settings.DEBUG else None
        }
    )


# 注册路由
app.include_router(auth.router, prefix="/api/auth", tags=["认证"])
app.include_router(channels.router, prefix="/api/channels", tags=["通道管理"])
app.include_router(monitor.router, prefix="/api/monitor", tags=["监控"])
app.include_router(realtime.router, prefix="/api/realtime", tags=["实时功能"])
app.include_router(system.router, prefix="/api/system", tags=["系统管理"])


@app.get("/", summary="根路径", description="API服务根路径，返回服务信息")
async def root():
    """根路径处理"""
    return {
        "service": "视频监控API服务",
        "version": "2.0.0",
        "status": "运行中",
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.get("/health", summary="健康检查", description="检查服务健康状态")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "video-monitor-api",
        "version": "2.0.0"
    }


def main():
    """主函数，用于直接运行服务"""
    settings = get_settings()
    uvicorn.run(
        "video_monitor_api.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )


if __name__ == "__main__":
    main()
